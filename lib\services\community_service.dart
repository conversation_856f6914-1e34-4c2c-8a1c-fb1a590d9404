import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/community_post.dart';
import '../utils/logger.dart';

class CommunityService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collections
  static const String _postsCollection = 'community_posts';
  static const String _commentsCollection = 'community_comments';
  static const String _statsCollection = 'community_stats';
  static const String _usersCollection = 'users';

  // Get current user
  static User? get currentUser => _auth.currentUser;
  static String get currentUserId => currentUser?.uid ?? 'anonymous';
  static String get currentUserName =>
      currentUser?.displayName ?? 'مستخدم مجهول';

  /// إنشاء منشور جديد
  static Future<String?> createPost({
    required String content,
    List<String> imageUrls = const [],
    Map<String, dynamic>? poll,
    List<String> tags = const [],
    String category = 'عام',
  }) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return null;
      }
      final post = CommunityPost(
        id: '', // سيتم تعيينه من Firebase
        authorId: currentUserId,
        authorName: currentUserName,
        content: content,
        timestamp: DateTime.now(),
        imageUrls: imageUrls,
        poll: poll,
        tags: tags,
        category: category,
      );

      final docRef = await _firestore
          .collection(_postsCollection)
          .add(post.toMap());

      // تحديث الإحصائيات
      await _updateStats();

      AppLogger.info('تم إنشاء منشور جديد: ${docRef.id}', 'CommunityService');
      return docRef.id;
    } catch (e) {
      AppLogger.error('خطأ في إنشاء المنشور', 'CommunityService', e);
      return null;
    }
  }

  /// الحصول على المنشورات
  static Stream<List<CommunityPost>> getPostsStream({
    String? category,
    int limit = 20,
  }) {
    try {
      Query query = _firestore
          .collection(_postsCollection)
          .orderBy('timestamp', descending: true);

      if (category != null && category != 'الكل') {
        query = query.where('category', isEqualTo: category);
      }

      query = query.limit(limit);

      return query.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) {
          return CommunityPost.fromMap(
            doc.data() as Map<String, dynamic>,
            doc.id,
          );
        }).toList();
      });
    } catch (e) {
      AppLogger.error('خطأ في جلب المنشورات', 'CommunityService', e);
      return Stream.value([]);
    }
  }

  /// إضافة إعجاب للمنشور
  static Future<bool> toggleLike(String postId) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return false;
      }
      final postRef = _firestore.collection(_postsCollection).doc(postId);

      return await _firestore.runTransaction((transaction) async {
        final postDoc = await transaction.get(postRef);

        if (!postDoc.exists) {
          throw Exception('المنشور غير موجود');
        }

        final data = postDoc.data() as Map<String, dynamic>;
        final likedBy = List<String>.from(data['likedBy'] ?? []);

        if (likedBy.contains(currentUserId)) {
          likedBy.remove(currentUserId);
        } else {
          likedBy.add(currentUserId);
        }

        transaction.update(postRef, {'likedBy': likedBy});
        return !likedBy.contains(currentUserId); // return new like status
      });
    } catch (e) {
      AppLogger.error('خطأ في تبديل الإعجاب', 'CommunityService', e);
      return false;
    }
  }

  /// إضافة تعليق
  static Future<String?> addComment({
    required String postId,
    required String content,
  }) async {
    try {
      // التحقق من تسجيل الدخول
      if (currentUser == null) {
        AppLogger.error('المستخدم غير مسجل الدخول', 'CommunityService');
        return null;
      }
      final comment = CommunityComment(
        id: '',
        postId: postId,
        authorId: currentUserId,
        authorName: currentUserName,
        content: content,
        timestamp: DateTime.now(),
      );

      final docRef = await _firestore
          .collection(_commentsCollection)
          .add(comment.toMap());

      // تحديث الإحصائيات
      await _updateStats();

      AppLogger.info(
        'تم إضافة تعليق جديد: ${docRef.id} للمنشور: $postId',
        'CommunityService',
      );
      return docRef.id;
    } catch (e) {
      AppLogger.error('خطأ في إضافة التعليق', 'CommunityService', e);
      return null;
    }
  }

  /// الحصول على التعليقات
  static Stream<List<CommunityComment>> getCommentsStream(String postId) {
    try {
      return _firestore
          .collection(_commentsCollection)
          .where('postId', isEqualTo: postId)
          .orderBy('timestamp', descending: false)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return CommunityComment.fromMap(doc.data(), doc.id);
            }).toList();
          });
    } catch (e) {
      AppLogger.error('خطأ في جلب التعليقات', 'CommunityService', e);
      return Stream.value([]);
    }
  }

  /// الحصول على عدد التعليقات
  static Future<int> getCommentsCount(String postId) async {
    try {
      final snapshot =
          await _firestore
              .collection(_commentsCollection)
              .where('postId', isEqualTo: postId)
              .get();

      return snapshot.docs.length;
    } catch (e) {
      AppLogger.error('خطأ في جلب عدد التعليقات', 'CommunityService', e);
      return 0;
    }
  }

  /// الحصول على الإحصائيات
  static Stream<CommunityStats> getStatsStream() {
    try {
      return _firestore
          .collection(_statsCollection)
          .doc('global')
          .snapshots()
          .map((doc) {
            if (doc.exists) {
              return CommunityStats.fromMap(doc.data() as Map<String, dynamic>);
            } else {
              return CommunityStats(
                totalUsers: 0,
                activeUsers: 0,
                totalPosts: 0,
                todayPosts: 0,
                totalComments: 0,
                todayComments: 0,
                lastUpdated: DateTime.now(),
              );
            }
          });
    } catch (e) {
      AppLogger.error('خطأ في جلب الإحصائيات', 'CommunityService', e);
      return Stream.value(
        CommunityStats(
          totalUsers: 0,
          activeUsers: 0,
          totalPosts: 0,
          todayPosts: 0,
          totalComments: 0,
          todayComments: 0,
          lastUpdated: DateTime.now(),
        ),
      );
    }
  }

  /// تحديث الإحصائيات
  static Future<void> _updateStats() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // حساب إجمالي المنشورات
      final totalPostsSnapshot =
          await _firestore.collection(_postsCollection).get();
      final totalPosts = totalPostsSnapshot.docs.length;

      // حساب منشورات اليوم
      final todayPostsSnapshot =
          await _firestore
              .collection(_postsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(today),
              )
              .get();
      final todayPosts = todayPostsSnapshot.docs.length;

      // حساب إجمالي التعليقات
      final totalCommentsSnapshot =
          await _firestore.collection(_commentsCollection).get();
      final totalComments = totalCommentsSnapshot.docs.length;

      // حساب تعليقات اليوم
      final todayCommentsSnapshot =
          await _firestore
              .collection(_commentsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(today),
              )
              .get();
      final todayComments = todayCommentsSnapshot.docs.length;

      // حساب المستخدمين النشطين (الذين نشروا أو علقوا في آخر 7 أيام)
      final weekAgo = now.subtract(const Duration(days: 7));
      final activeUsersSet = <String>{};

      final recentPostsSnapshot =
          await _firestore
              .collection(_postsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(weekAgo),
              )
              .get();

      for (final doc in recentPostsSnapshot.docs) {
        final data = doc.data();
        activeUsersSet.add(data['authorId'] ?? '');
      }

      final recentCommentsSnapshot =
          await _firestore
              .collection(_commentsCollection)
              .where(
                'timestamp',
                isGreaterThanOrEqualTo: Timestamp.fromDate(weekAgo),
              )
              .get();

      for (final doc in recentCommentsSnapshot.docs) {
        final data = doc.data();
        activeUsersSet.add(data['authorId'] ?? '');
      }

      final activeUsers = activeUsersSet.length;

      // حساب إجمالي المستخدمين المسجلين
      final totalUsersSnapshot =
          await _firestore.collection(_usersCollection).get();
      final totalUsers = totalUsersSnapshot.docs.length;

      final stats = CommunityStats(
        totalUsers: totalUsers,
        activeUsers: activeUsers,
        totalPosts: totalPosts,
        todayPosts: todayPosts,
        totalComments: totalComments,
        todayComments: todayComments,
        lastUpdated: now,
      );

      await _firestore
          .collection(_statsCollection)
          .doc('global')
          .set(stats.toMap());

      AppLogger.info(
        'تم تحديث الإحصائيات - المنشورات: $totalPosts، اليوم: $todayPosts، المستخدمون: $activeUsers',
        'CommunityService',
      );
    } catch (e) {
      AppLogger.error('خطأ في تحديث الإحصائيات', 'CommunityService', e);
    }
  }

  /// تهيئة الإحصائيات
  static Future<void> initializeStats() async {
    try {
      await _updateStats();
      AppLogger.info('تم تهيئة إحصائيات المجتمع', 'CommunityService');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة الإحصائيات', 'CommunityService', e);
    }
  }
}
